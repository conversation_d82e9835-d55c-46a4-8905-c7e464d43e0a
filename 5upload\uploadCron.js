// @ts-check
import { config } from 'dotenv';
config();
import { createClient } from '@supabase/supabase-js';
import { OpenDriveClient } from '../openDrive.js';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import cron from 'node-cron';
import { Client } from '@microsoft/microsoft-graph-client';
import 'isomorphic-fetch';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const TEMP_DOWNLOAD_DIR = path.join(__dirname, 'temp_downloads');
const MAX_RECORDS_TO_CHECK = 10;

/**
 * Sanitizes filename for file system compatibility
 * @param {string} filename - The original filename
 * @returns {string} - The sanitized filename
 */
function sanitizeFilename(filename) {
  return filename
    .replace(/:/g, '-')    // Replace colons with hyphens
    .replace(/\[/g, '(')   // Replace square brackets with parentheses
    .replace(/\]/g, ')')
    .replace(/'/g, '')     // Remove single quotes
    .replace(/"/g, "'")    // Replace straight quotes with smart quotes
    .replace(/[※]/g, '')  // Remove special characters like ※
    .replace(/[<>:"/\\|?*]/g, '_'); // Replace other invalid Windows filename characters
}

const credentials = {
  clientId: process.env.CLIENT_ID,
  clientSecret: process.env.CLIENT_SECRET,
  tenantId: process.env.TENANT_ID
};

if (!fs.existsSync(TEMP_DOWNLOAD_DIR)) {
  fs.mkdirSync(TEMP_DOWNLOAD_DIR, { recursive: true });
}

const supabase = createClient(
  // @ts-ignore
  process.env.SUPABASE_PROJECT_URL,
  process.env.SUPABASE_ADMIN_KEY
);

const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  CYAN: '\x1b[36m',
  GRAY: '\x1b[90m',
};

async function getAccessToken() {
  try {
    const response = await fetch(
      `https://login.microsoftonline.com/${credentials.tenantId}/oauth2/v2.0/token`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        // @ts-ignore
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: credentials.clientId,
          client_secret: credentials.clientSecret,
          scope: 'https://graph.microsoft.com/.default'
        })
      }
    );
    const { access_token } = await response.json();
    return access_token;
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to get access token: ${error.message}${COLORS.RESET}`);
    throw error;
  }
}

async function uploadToSharePoint(filePath, fileName) {
  try {
    console.log(`[DEBUG] Starting SharePoint upload for ${fileName}`);
    
    // Sanitize filename before processing
    const sanitizedFileName = fileName
      .replace(/:/g, '-')    // Replace colons with hyphens
      .replace(/\[/g, '(')   // Replace square brackets with parentheses
      .replace(/\]/g, ')')
      .replace(/'/g, '')     // Remove single quotes
      .replace(/"/g, '’');   // Replace straight quotes with smart quotes

    const encodedFileName = encodeURIComponent(sanitizedFileName);
    console.log(`[CLEAN] Original filename: ${fileName}`);
    console.log(`[CLEAN] Sanitized filename: ${sanitizedFileName}`);
    console.log(`[CLEAN] Encoded filename: ${encodedFileName}`);

    // Authentication
    console.log('[AUTH] Retrieving access token...');
    const accessToken = await getAccessToken();
    console.log('[AUTH] Access token obtained successfully');

    const client = Client.init({
      authProvider: (done) => done(null, accessToken)
    });

    // Site configuration
    const siteId = 'lycoriscafe-my.sharepoint.com,8ecfe79d-d157-4235-8929-124ef91868bf,ea42ba5c-18e7-4651-8762-a1b45c23400a';
    console.log(`[SITE] Using site ID: ${siteId}`);
    
    // Drive discovery
    console.log('[DRIVE] Fetching available drives...');
    const driveResponse = await client.api(`/sites/${siteId}/drives`).get();
    console.log(`[DRIVE] Found ${driveResponse.value.length} drive(s)`);
    
    if (!driveResponse.value.length) {
      throw new Error('No drives found in specified site');
    }

    const drive = driveResponse.value[0];
    console.log(`[DRIVE] Selected drive: ${drive.id} (${drive.name})`);

    // Verify root folder access
    console.log('[DRIVE] Checking root folder access...');
    const rootFolder = await client.api(`/drives/${drive.id}/root`).get();
    console.log(`[DRIVE] Root folder ID: ${rootFolder.id}`);

    // File preparation
    console.log(`[FILE] Reading file from ${filePath}`);
    const fileContent = fs.readFileSync(filePath);
    const fileStats = fs.statSync(filePath);
    console.log(`[FILE] File size: ${fileStats.size} bytes`);

    // Create upload session
    const uploadSessionUrl = `/drives/${drive.id}/root:/${encodedFileName}:/createUploadSession`;
    console.log(`[UPLOAD] Creating session via: ${uploadSessionUrl}`);
    
    const uploadSession = await client.api(uploadSessionUrl).post({});
    console.log(`[UPLOAD] Session created: ${uploadSession.uploadUrl}`);

    // Chunked upload
    const maxSliceSize = 60 * 1024 * 1024;
    const fileSize = fileStats.size;
    let start = 0;
    let chunkCount = 0;

    console.log(`[UPLOAD] Starting chunked upload (${Math.ceil(fileSize/maxSliceSize)} chunks)`);
    
    while (start < fileSize) {
      chunkCount++;
      const end = Math.min(start + maxSliceSize, fileSize);
      const slice = fileContent.slice(start, end);

      console.log(`[CHUNK ${chunkCount}] Uploading bytes ${start}-${end-1} (${end-start} bytes)`);
      
      const uploadResponse = await fetch(uploadSession.uploadUrl, {
        method: 'PUT',
        headers: {
          'Content-Length': `${end - start}`,
          'Content-Range': `bytes ${start}-${end - 1}/${fileSize}`,
        },
        body: slice
      });

      if (!uploadResponse.ok) {
        const errorBody = await uploadResponse.text();
        console.error(`[CHUNK ${chunkCount}] Upload failed: ${errorBody}`);
        throw new Error(`Chunk upload failed at byte ${start}`);
      }

      console.log(`[CHUNK ${chunkCount}] Upload successful`);
      start = end;
    }

    // File verification
    const fileVerifyUrl = `/drives/${drive.id}/root:/${encodedFileName}`;
    console.log(`[VERIFY] Fetching uploaded file from: ${fileVerifyUrl}`);
    
    const uploadedFile = await client.api(fileVerifyUrl).get();
    console.log(`[VERIFY] File verified: ${uploadedFile.id} (${uploadedFile.size} bytes)`);

    // Share link creation
    console.log(`[SHARE] Creating anonymous view link for item ${uploadedFile.id}`);
    const sharingLink = await client
      .api(`/drives/${drive.id}/items/${uploadedFile.id}/createLink`)
      .post({
        type: 'view',
        scope: 'anonymous'
      });

    console.log(`[SHARE] Sharing link created: ${sharingLink.link.webUrl}`);

    // Construct final URL with sanitized filename
    const publicUrl = `https://lycoriscafe-my.sharepoint.com/personal/lycoriscafe_lycoriscafe_onmicrosoft_com/Documents/${sanitizedFileName}`;
    console.log(`[COMPLETE] Upload successful: ${publicUrl}`);
    
    return publicUrl;
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Critical upload failure: ${error.message}${COLORS.RESET}`);
    console.error('[ERROR] Stack trace:', error.stack);
    
    if (error?.response) {
      console.error('[ERROR] API Response:', {
        status: error.response.status,
        headers: error.response.headers,
        body: await error.response.text()
      });
    }
    
    throw error;
  }
}

async function downloadFromPixeldrain(url, outputPath) {
  try {
    console.info(`${COLORS.CYAN}[INFO] Downloading from Pixeldrain: ${url}${COLORS.RESET}`);
    const response = await axios({
      method: 'GET',
      url,
      responseType: 'stream'
    });

    const writer = fs.createWriteStream(outputPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      // @ts-ignore
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to download from Pixeldrain: ${error.message}${COLORS.RESET}`);
    throw error;
  }
}

async function processForOpenDrive(quality, url, episode, openDrive) {
  try {
    let resolutionLabel;
    switch (quality) {
      case 'FHD':
        resolutionLabel = '1080p';
        break;
      case 'HD':
        resolutionLabel = '720p';
        break;
      case 'SD':
        resolutionLabel = '480p';
        break;
      case 'SourceMKV':
        resolutionLabel = '1080p';
        break;
      default:
        resolutionLabel = quality;
    }

    const fileExtension = quality === 'SourceMKV' ? 'mkv' : 'mp4';
    const rawFilename = `[lycoris.cafe] ${episode.anime_title} - ${episode.episode_number} [${resolutionLabel}].${fileExtension}`;
    
    // Sanitize filename for file system compatibility
    const sanitizedFilename = sanitizeFilename(rawFilename);

    console.log(`[CLEAN] Original filename: ${rawFilename}`);
    console.log(`[CLEAN] Sanitized filename: ${sanitizedFilename}`);

    const downloadPath = path.join(TEMP_DOWNLOAD_DIR, `${episode.id}_${sanitizedFilename}`);

    await downloadFromPixeldrain(url, downloadPath);

    console.info(`${COLORS.CYAN}[INFO] Uploading ${sanitizedFilename} to OpenDrive${COLORS.RESET}`);
    const opendriveResult = await openDrive.uploadFile(downloadPath, sanitizedFilename);

    fs.unlinkSync(downloadPath);

    return { quality, link: opendriveResult.DownloadLink };
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to process ${quality} for OpenDrive: ${error.message}${COLORS.RESET}`);
    throw error;
  }
}

async function processEpisode(episode, openDrive) {
  try {
    const secondary_source = episode.secondary_source;

    // Only handle SharePoint upload if burst_source is set to "update_me"
    if (episode.burst_source === 'update_me') {
      if (secondary_source.FHD && secondary_source.FHD.includes('pixeldrain.com')) {
        const rawFilename = `[lycoris.cafe] ${episode.anime_title} - ${episode.episode_number} [1080p].mp4`;

        // Sanitize filename for file system compatibility
        const sanitizedFilename = sanitizeFilename(rawFilename);

        console.log(`[SHAREPOINT] Original filename: ${rawFilename}`);
        console.log(`[SHAREPOINT] Sanitized filename: ${sanitizedFilename}`);

        const downloadPath = path.join(TEMP_DOWNLOAD_DIR, `${episode.id}_sharepoint_${sanitizedFilename}`);

        await downloadFromPixeldrain(secondary_source.FHD, downloadPath);
        console.info(`${COLORS.CYAN}[INFO] Uploading ${sanitizedFilename} to SharePoint${COLORS.RESET}`);
        const sharePointUrl = await uploadToSharePoint(downloadPath, rawFilename); // Use original filename for SharePoint
        fs.unlinkSync(downloadPath);

        // Update burst_source with SharePoint URL
        await supabase
          .from('anime')
          .update({ burst_source: sharePointUrl })
          .eq('id', episode.id);
      } else {
        console.info(`${COLORS.GRAY}[INFO] Cannot update burst_source for episode ${episode.id} - FHD quality not available${COLORS.RESET}`);
      }
    }

    // Then, handle OpenDrive uploads (all qualities)
    const requiredQualities = ['FHD', 'HD', 'SD', 'SourceMKV'];
    const missingQualities = requiredQualities.filter(quality =>
      !secondary_source[quality] || !secondary_source[quality].includes('pixeldrain.com')
    );

    if (missingQualities.length > 0) {
      console.info(`${COLORS.GRAY}[INFO] Skipping OpenDrive upload for episode ${episode.id} - Missing qualities: ${missingQualities.join(', ')}${COLORS.RESET}`);
      return;
    }

    const uploadPromises = Object.entries(secondary_source)
      .filter(([quality, url]) => url && url.includes('pixeldrain.com'))
      .map(([quality, url]) => processForOpenDrive(quality, url, episode, openDrive));

    const results = await Promise.all(uploadPromises);

    const primary_source = results.reduce((acc, { quality, link }) => {
      acc[quality] = link;
      return acc;
    }, {});

    // Update primary_source with OpenDrive links
    const { error } = await supabase
      .from('anime')
      .update({
        primary_source: primary_source,
        needs_update: false
      })
      .eq('id', episode.id);

    if (error) {
      throw new Error(`Database update failed: ${error.message}`);
    }

    console.info(`${COLORS.GREEN}[SUCCESS] Successfully processed episode ${episode.id}${COLORS.RESET}`);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to process episode ${episode.id}: ${error.message}${COLORS.RESET}`);

    const files = fs.readdirSync(TEMP_DOWNLOAD_DIR);
    for (const file of files) {
      if (file.startsWith(`${episode.id}_`)) {
        fs.unlinkSync(path.join(TEMP_DOWNLOAD_DIR, file));
      }
    }
    throw error;
  }
}

async function main() {
  try {
    const { data: episodes, error } = await supabase
      .from('anime')
      .select('*')
      .or('primary_source.eq.{},needs_update.eq.true,burst_source.eq.update_me')
      .order('id', { ascending: false })
      .limit(MAX_RECORDS_TO_CHECK);

    if (error) {
      throw new Error(`Database query failed: ${error.message}`);
    }

    if (!episodes || episodes.length === 0) {
      console.info(`${COLORS.GRAY}[INFO] No episodes requiring processing${COLORS.RESET}`);
      return;
    }

    const openDrive = new OpenDriveClient();
    console.info(`${COLORS.CYAN}[INFO] Authenticating with OpenDrive...${COLORS.RESET}`);
    await openDrive.authenticate();
    console.info(`${COLORS.GREEN}[SUCCESS] Successfully authenticated with OpenDrive${COLORS.RESET}`);

    console.info(`${COLORS.CYAN}[INFO] Found ${episodes.length} episodes to process${COLORS.RESET}`);

    for (const episode of episodes) {
      await processEpisode(episode, openDrive);
    }

  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Script execution failed: ${error.message}${COLORS.RESET}`);
  } finally {
    if (fs.existsSync(TEMP_DOWNLOAD_DIR)) {
      fs.readdirSync(TEMP_DOWNLOAD_DIR).forEach(file => {
        fs.unlinkSync(path.join(TEMP_DOWNLOAD_DIR, file));
      });
    }
  }
}

cron.schedule('*/15 * * * *', () => {
  console.info(`${COLORS.CYAN}[INFO] Starting scheduled task at ${new Date().toISOString()}${COLORS.RESET}`);
  main().catch(error => {
    console.error(`${COLORS.RED}[ERROR] Scheduled task failed: ${error.message}${COLORS.RESET}`);
  });
}, {
  scheduled: true,
  timezone: "Europe/Warsaw"
});

main()